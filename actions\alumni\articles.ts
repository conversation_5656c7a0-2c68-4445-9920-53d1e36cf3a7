import { z } from "zod";
import { getSession } from "../account/user";
import { PrismaClient } from "../../prisma/generated/client";

const prisma = new PrismaClient();

// Type definitions
type ArticleCategory =
  | "GENERAL"
  | "EVENTS"
  | "SUCCESS_STORIES"
  | "OPPORTUNITIES"
  | "ANNOUNCEMENTS";

export interface ArticleWithDetails {
  id: string;
  title: string;
  content: string;
  excerpt?: string | null;
  category:
    | "GENERAL"
    | "EVENTS"
    | "SUCCESS_STORIES"
    | "OPPORTUNITIES"
    | "ANNOUNCEMENTS";
  imageUrl?: string | null;
  published: boolean;
  publishedAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

type ArticleResponse = {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
};

/**
 * Create a new article
 */
export async function createArticle({
  title,
  content,
  excerpt,
  category,
  tags,
  imageUrl,
  published = false,
}: {
  title: string;
  content: string;
  excerpt?: string;
  category: ArticleCategory;
  tags?: string[];
  imageUrl?: string;
  published?: boolean;
}): Promise<ArticleResponse> {
  try {
    // Validate session
    const session = await getSession();
    if (!session) {
      return {
        success: false,
        message: "Authentication required",
        error: "UNAUTHORIZED",
      };
    }

    // Validate input
    const schema = z.object({
      title: z.string().min(3).max(200),
      content: z.string().min(10),
      excerpt: z.string().max(500).optional(),
      category: z.enum([
        "GENERAL",
        "EVENTS",
        "SUCCESS_STORIES",
        "OPPORTUNITIES",
        "ANNOUNCEMENTS",
      ]),
      tags: z.array(z.string()).optional(),
      imageUrl: z.string().url().optional(),
      published: z.boolean().default(false),
    });

    const validationResult = schema.safeParse({
      title,
      content,
      excerpt,
      category,
      tags,
      imageUrl,
      published,
    });

    if (!validationResult.success) {
      return {
        success: false,
        message: "Invalid article data",
        error: validationResult.error.message,
      };
    }

    // Check if user has an alumni profile
    const authorProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!authorProfile) {
      return {
        success: false,
        message: "Alumni profile required to create articles",
        error: "PROFILE_REQUIRED",
      };
    }

    // Create the article
    const article = await prisma.newsArticle.create({
      data: {
        title,
        content,
        excerpt: excerpt || title.substring(0, 100),
        category,
        tags: tags || [],
        imageUrl,
        published: published || false,
        publishedAt: published ? new Date() : null,
      },
    });

    return {
      success: true,
      message: "Article created successfully",
      data: article,
    };
  } catch (error: any) {
    console.error("Error creating article:", error);
    return {
      success: false,
      message: "Failed to create article",
      error: error.message,
    };
  }
}

/**
 * Update an existing article
 */
export async function updateArticle({
  articleId,
  title,
  content,
  excerpt,
  imageUrl,
  category,
  tags,
  published,
}: {
  articleId: string;
  title?: string;
  content?: string;
  excerpt?: string;
  imageUrl?: string;
  category?: string;
  tags?: string[];
  published?: boolean;
}): Promise<ArticleResponse> {
  try {
    // Validate session
    const session = await getSession();
    if (!session) {
      return {
        success: false,
        message: "Authentication required",
        error: "UNAUTHORIZED",
      };
    }

    // Validate input
    const schema = z.object({
      articleId: z.string().uuid(),
      title: z.string().min(3).max(200).optional(),
      content: z.string().min(10).optional(),
      excerpt: z.string().max(500).optional(),
      category: z
        .enum([
          "GENERAL",
          "EVENTS",
          "SUCCESS_STORIES",
          "OPPORTUNITIES",
          "ANNOUNCEMENTS",
        ])
        .optional(),
      tags: z.array(z.string()).optional(),
      imageUrl: z.string().url().optional(),
      published: z.boolean().optional(),
    });

    const validationResult = schema.safeParse({
      articleId,
      title,
      content,
      excerpt,
      category,
      imageUrl,
      published,
    });

    if (!validationResult.success) {
      return {
        success: false,
        message: "Invalid article data",
        error: validationResult.error.message,
      };
    }

    // Get user's alumni profile
    const authorProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!authorProfile) {
      return {
        success: false,
        message: "Alumni profile required to update articles",
        error: "PROFILE_REQUIRED",
      };
    }

    // Check if article exists
    const existingArticle = await prisma.newsArticle.findUnique({
      where: { id: articleId },
    });

    if (!existingArticle) {
      return {
        success: false,
        message: "Article not found",
        error: "NOT_FOUND",
      };
    }

    // Note: NewsArticle model doesn't have author relationship
    // Only admins can update articles in this implementation
    const isAdmin = session.role === "admin";
    if (!isAdmin) {
      return {
        success: false,
        message: "Only administrators can update articles",
        error: "UNAUTHORIZED",
      };
    }

    // Update the article
    const updateData: any = { updatedAt: new Date() };
    if (title !== undefined) updateData.title = title;
    if (content !== undefined) updateData.content = content;
    if (excerpt !== undefined) updateData.excerpt = excerpt;
    if (imageUrl !== undefined) updateData.imageUrl = imageUrl;
    if (category !== undefined) updateData.category = category;
    if (tags !== undefined) updateData.tags = tags;
    if (published !== undefined) {
      updateData.published = published;
      updateData.publishedAt = published ? new Date() : null;
    }

    const updatedArticle = await prisma.newsArticle.update({
      where: { id: articleId },
      data: updateData,
    });

    return {
      success: true,
      message: "Article updated successfully",
      data: updatedArticle,
    };
  } catch (error: any) {
    console.error("Error updating article:", error);
    return {
      success: false,
      message: "Failed to update article",
      error: error.message,
    };
  }
}

/**
 * Get articles with pagination and filtering
 */
export async function getArticles({
  page = 1,
  limit = 10,
  category,
  published,
  search,
}: {
  page?: number;
  limit?: number;
  category?:
    | "GENERAL"
    | "EVENTS"
    | "SUCCESS_STORIES"
    | "OPPORTUNITIES"
    | "ANNOUNCEMENTS";
  published?: boolean;
  search?: string;
}) {
  try {
    // Validate session
    const session = await getSession();
    if (!session) {
      return {
        success: false,
        message: "Authentication required",
        error: "UNAUTHORIZED",
      };
    }

    // Build where clause
    const where: any = {};
    if (published !== undefined) where.published = published;
    if (category) where.category = category;
    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { content: { contains: search, mode: "insensitive" } },
        { excerpt: { contains: search, mode: "insensitive" } },
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await prisma.newsArticle.count({ where });

    // Get articles with author information
    const articles = await prisma.newsArticle.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: "desc" },
    });

    return {
      success: true,
      message: "Articles retrieved successfully",
      data: {
        articles,
        pagination: {
          total: totalCount,
          page,
          limit,
          totalPages: Math.ceil(totalCount / limit),
        },
      },
    };
  } catch (error: any) {
    console.error("Error getting articles:", error);
    return {
      success: false,
      message: "Failed to retrieve articles",
      error: error.message,
    };
  }
}

/**
 * Get a single article by ID
 */
export async function getArticleById(
  articleId: string
): Promise<{ success: boolean; data?: ArticleWithDetails; error?: string }> {
  try {
    // Validate session
    const session = await getSession();
    if (!session) {
      return {
        success: false,
        message: "Authentication required",
        error: "UNAUTHORIZED",
      };
    }

    // Get the article with author information
    const article = await prisma.newsArticle.findUnique({
      where: { id: articleId },
    });

    if (!article) {
      return {
        success: false,
        message: "Article not found",
        error: "NOT_FOUND",
      };
    }

    // Check if article is published
    if (!article.published) {
      return {
        success: false,
        message: "Article is not published",
        error: "UNAUTHORIZED",
      };
    }

    return {
      success: true,
      message: "Article retrieved successfully",
      data: article,
    };
  } catch (error: any) {
    console.error("Error getting article:", error);
    return {
      success: false,
      message: "Failed to retrieve article",
      error: error.message,
    };
  }
}

/**
 * Delete an article
 */
export async function deleteArticle({
  articleId,
}: {
  articleId: string;
}): Promise<ArticleResponse> {
  try {
    // Validate session
    const session = await getSession();
    if (!session) {
      return {
        success: false,
        message: "Authentication required",
        error: "UNAUTHORIZED",
      };
    }

    // Get user's alumni profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return {
        success: false,
        message: "Alumni profile required",
        error: "PROFILE_REQUIRED",
      };
    }

    // Check if article exists
    const article = await prisma.newsArticle.findUnique({
      where: { id: articleId },
    });

    if (!article) {
      return {
        success: false,
        message: "Article not found",
        error: "NOT_FOUND",
      };
    }

    // Check if user is an admin (NewsArticle doesn't have author relationship)
    const isAdmin = session.role === "admin";

    if (!isAdmin) {
      return {
        success: false,
        message: "Only administrators can delete articles",
        error: "UNAUTHORIZED",
      };
    }

    // Delete the article
    await prisma.newsArticle.delete({
      where: { id: articleId },
    });

    return {
      success: true,
      message: "Article deleted successfully",
    };
  } catch (error: any) {
    console.error("Error deleting article:", error);
    return {
      success: false,
      message: "Failed to delete article",
      error: error.message,
    };
  }
}

/**
 * Get featured articles
 */
export async function getFeaturedArticles({
  limit = 5,
}: {
  limit?: number;
}): Promise<ArticleResponse> {
  try {
    // Validate session
    const session = await getSession();
    if (!session) {
      return {
        success: false,
        message: "Authentication required",
        error: "UNAUTHORIZED",
      };
    }

    // Get featured articles (published)
    const articles = await prisma.newsArticle.findMany({
      where: {
        published: true,
      },
      take: limit,
      orderBy: [{ createdAt: "desc" }],
    });

    return {
      success: true,
      message: "Featured articles retrieved successfully",
      data: articles,
    };
  } catch (error: any) {
    console.error("Error getting featured articles:", error);
    return {
      success: false,
      message: "Failed to retrieve featured articles",
      error: error.message,
    };
  }
}

/**
 * Get articles by author - NOT SUPPORTED
 * NewsArticle model doesn't have author relationship
 */
export async function getArticlesByAuthor({
  page = 1,
  limit = 10,
  includeNonPublished = false,
}: {
  page?: number;
  limit?: number;
  includeNonPublished?: boolean;
}): Promise<ArticleResponse> {
  try {
    // Validate session
    const session = await getSession();
    if (!session) {
      return {
        success: false,
        message: "Authentication required",
        error: "UNAUTHORIZED",
      };
    }

    // NewsArticle model doesn't have author relationship
    // This function is not applicable for the current schema
    return {
      success: false,
      message:
        "Articles by author not supported - NewsArticle model doesn't have author relationship",
      error: "NOT_SUPPORTED",
    };
  } catch (error: any) {
    console.error("Error getting author's articles:", error);
    return {
      success: false,
      message: "Failed to retrieve author's articles",
      error: error.message,
    };
  }
}

/**
 * Get article statistics
 */
export async function getArticleStats(): Promise<ArticleResponse> {
  try {
    // Validate session
    const session = await getSession();
    if (!session) {
      return {
        success: false,
        message: "Authentication required",
        error: "UNAUTHORIZED",
      };
    }

    // Get user's alumni profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    // Get total articles count
    const totalArticles = await prisma.newsArticle.count({
      where: { published: true },
    });

    // Get articles by category
    const articlesByCategory = await prisma.newsArticle.groupBy({
      by: ["category"],
      where: { published: true },
      _count: { category: true },
    });

    // User stats not available since NewsArticle doesn't have author relationship
    let userStats = null;

    // Get recent activity
    const recentArticles = await prisma.newsArticle.findMany({
      where: { published: true },
      take: 5,
      orderBy: { createdAt: "desc" },
      select: {
        id: true,
        title: true,
        createdAt: true,
      },
    });

    return {
      success: true,
      message: "Article statistics retrieved successfully",
      data: {
        totalArticles,
        articlesByCategory,
        userStats,
        recentArticles,
      },
    };
  } catch (error: any) {
    console.error("Error getting article statistics:", error);
    return {
      success: false,
      message: "Failed to retrieve article statistics",
      error: error.message,
    };
  }
}
