"use server";

import { getSession } from "@/actions/account/user";
import { prisma } from "@/prisma/prisma";
import { pusherServer } from "@/lib/pusher";

interface SendMessageData {
  recipientId: string;
  content: string;
  conversationId?: string;
}

// Send a message
export async function sendMessage(data: SendMessageData) {
  try {
    const session = await getSession();

    // Get sender's profile
    const senderProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!senderProfile) {
      return {
        success: false,
        error: "You must create a profile before sending messages",
      };
    }

    // Get recipient's profile
    const recipientProfile = await prisma.alumniProfile.findUnique({
      where: { userId: data.recipientId },
    });

    if (!recipientProfile) {
      return { success: false, error: "Recipient not found" };
    }

    // Check if users are connected (optional - you might want to allow messaging without connection)
    const connection = await prisma.connection.findFirst({
      where: {
        OR: [
          {
            senderId: senderProfile.id,
            receiverId: recipientProfile.id,
            status: "ACCEPTED",
          },
          {
            senderId: recipientProfile.id,
            receiverId: senderProfile.id,
            status: "ACCEPTED",
          },
        ],
      },
    });

    if (!connection) {
      return { success: false, error: "You can only message connected alumni" };
    }

    let conversationId = data.conversationId;

    // If no conversation ID provided, find or create conversation
    if (!conversationId) {
      let conversation = await prisma.conversation.findFirst({
        where: {
          participants: {
            hasEvery: [senderProfile.id, recipientProfile.id],
          },
          organizationId: session.org,
        },
      });

      if (!conversation) {
        // Create new conversation
        conversation = await prisma.conversation.create({
          data: {
            participants: [senderProfile.id, recipientProfile.id],
            organizationId: session.org,
            lastMessageAt: new Date(),
          },
        });
      }

      conversationId = conversation.id;
    }

    // Create the message
    const message = await prisma.message.create({
      data: {
        content: data.content,
        senderId: senderProfile.id,
        conversationId,
        organizationId: session.org,
      },
      include: {
        sender: {
          include: {
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
      },
    });

    // Update conversation's last message timestamp
    await prisma.conversation.update({
      where: { id: conversationId },
      data: {
        lastMessageAt: new Date(),
      },
    });

    // Get conversation participants for real-time notifications
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
    });

    if (conversation) {
      // Send real-time message to all participants except the sender
      const otherParticipants = conversation.participants.filter(
        (participantId) => participantId !== senderProfile.id
      );

      // Trigger Pusher event for each participant
      for (const participantId of otherParticipants) {
        try {
          await pusherServer.trigger(
            `private-user-${participantId}`,
            "new-message",
            {
              message: {
                id: message.id,
                content: message.content,
                senderId: message.senderId,
                conversationId: message.conversationId,
                createdAt: message.createdAt,
                sender: {
                  id: senderProfile.id,
                  firstName: senderProfile.firstName,
                  lastName: senderProfile.lastName,
                  profilePicture: senderProfile.profilePicture,
                  user: {
                    name: message.sender.user.name,
                    image: message.sender.user.image,
                  },
                },
              },
              conversationId,
            }
          );

          // Also trigger conversation list update
          await pusherServer.trigger(
            `private-user-${participantId}`,
            "conversation-updated",
            {
              conversationId,
              lastMessageAt: new Date(),
              lastMessage: {
                id: message.id,
                content: message.content,
                senderId: message.senderId,
                createdAt: message.createdAt,
                sender: {
                  firstName: senderProfile.firstName,
                  lastName: senderProfile.lastName,
                  profilePicture: senderProfile.profilePicture,
                  user: {
                    name: message.sender.user.name,
                    image: message.sender.user.image,
                  },
                },
              },
            }
          );
        } catch (pusherError) {
          console.error("Error sending Pusher notification:", pusherError);
          // Don't fail the message send if Pusher fails
        }
      }
    }

    return { success: true, message };
  } catch (error) {
    console.error("Error sending message:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to send message",
    };
  }
}

// Get conversations for current user
export async function getConversations(page = 1, limit = 20) {
  try {
    const session = await getSession();

    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    // Get conversations where user is a participant
    const [conversations, total] = await Promise.all([
      prisma.conversation.findMany({
        where: {
          participants: {
            has: userProfile.id,
          },
          organizationId: session.org,
        },
        include: {
          messages: {
            orderBy: {
              createdAt: "desc",
            },
            take: 1,
            include: {
              sender: {
                select: {
                  firstName: true,
                  lastName: true,
                  profilePicture: true,
                  user: {
                    select: {
                      name: true,
                      image: true,
                    },
                  },
                },
              },
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          lastMessageAt: "desc",
        },
      }),
      prisma.conversation.count({
        where: {
          participants: {
            has: userProfile.id,
          },
          organizationId: session.org,
        },
      }),
    ]);

    // Get other participants' info for each conversation
    const conversationsWithParticipants = await Promise.all(
      conversations.map(async (conversation) => {
        const otherParticipantIds = conversation.participants.filter(
          (id) => id !== userProfile.id
        );

        const otherParticipants = await prisma.alumniProfile.findMany({
          where: {
            id: {
              in: otherParticipantIds,
            },
          },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePicture: true,
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        });

        // Count unread messages
        const unreadCount = await prisma.message.count({
          where: {
            conversationId: conversation.id,
            senderId: {
              not: userProfile.id,
            },
            readBy: {
              not: {
                has: userProfile.id,
              },
            },
          },
        });

        return {
          ...conversation,
          otherParticipants,
          unreadCount,
          lastMessage: conversation.messages[0] || null,
        };
      })
    );

    return {
      success: true,
      conversations: conversationsWithParticipants,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Error fetching conversations:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch conversations",
    };
  }
}

// Get messages in a conversation
export async function getMessages(
  conversationId: string,
  page = 1,
  limit = 50
) {
  try {
    const session = await getSession();

    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    // Verify user is part of the conversation
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
    });

    if (!conversation || !conversation.participants.includes(userProfile.id)) {
      return {
        success: false,
        error: "Conversation not found or access denied",
      };
    }

    // Get messages with pagination
    const [messages, total] = await Promise.all([
      prisma.message.findMany({
        where: {
          conversationId,
        },
        include: {
          sender: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              profilePicture: true,
              user: {
                select: {
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: "desc",
        },
      }),
      prisma.message.count({
        where: {
          conversationId,
        },
      }),
    ]);

    // Reverse messages to show oldest first
    const orderedMessages = messages.reverse();

    return {
      success: true,
      messages: orderedMessages,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Error fetching messages:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch messages",
    };
  }
}

// Mark messages as read
export async function markMessagesAsRead(conversationId: string) {
  try {
    const session = await getSession();

    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    // Verify user is part of the conversation
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
    });

    if (!conversation || !conversation.participants.includes(userProfile.id)) {
      return {
        success: false,
        error: "Conversation not found or access denied",
      };
    }

    // Get unread messages in the conversation
    const unreadMessages = await prisma.message.findMany({
      where: {
        conversationId,
        senderId: {
          not: userProfile.id,
        },
        readBy: {
          not: {
            has: userProfile.id,
          },
        },
      },
    });

    // Mark messages as read by adding user ID to readBy array
    await Promise.all(
      unreadMessages.map((message) =>
        prisma.message.update({
          where: { id: message.id },
          data: {
            readBy: {
              push: userProfile.id,
            },
          },
        })
      )
    );

    // Send real-time read receipt notifications to other participants
    if (unreadMessages.length > 0) {
      const otherParticipants = conversation.participants.filter(
        (participantId) => participantId !== userProfile.id
      );

      for (const participantId of otherParticipants) {
        try {
          await pusherServer.trigger(
            `private-user-${participantId}`,
            "messages-read",
            {
              conversationId,
              readByUserId: userProfile.id,
              readByUser: {
                id: userProfile.id,
                firstName: userProfile.firstName,
                lastName: userProfile.lastName,
                profilePicture: userProfile.profilePicture,
              },
              messageIds: unreadMessages.map((msg) => msg.id),
              readAt: new Date(),
            }
          );
        } catch (pusherError) {
          console.error(
            "Error sending read receipt notification:",
            pusherError
          );
          // Don't fail the operation if Pusher fails
        }
      }
    }

    return { success: true, markedCount: unreadMessages.length };
  } catch (error) {
    console.error("Error marking messages as read:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to mark messages as read",
    };
  }
}

// Delete a message (soft delete)
export async function deleteMessage(messageId: string) {
  try {
    const session = await getSession();

    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    // Find the message
    const message = await prisma.message.findUnique({
      where: { id: messageId },
    });

    if (!message) {
      return { success: false, error: "Message not found" };
    }

    // Verify user is the sender
    if (message.senderId !== userProfile.id) {
      return { success: false, error: "You can only delete your own messages" };
    }

    // Soft delete by updating content
    const deletedMessage = await prisma.message.update({
      where: { id: messageId },
      data: {
        content: "[Message deleted]",
        isDeleted: true,
        updatedAt: new Date(),
      },
    });

    return { success: true, message: deletedMessage };
  } catch (error) {
    console.error("Error deleting message:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to delete message",
    };
  }
}

// Get messaging statistics
export async function getMessagingStats() {
  try {
    const session = await getSession();

    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    // Get messaging statistics
    const [totalConversations, totalMessages, unreadMessages] =
      await Promise.all([
        prisma.conversation.count({
          where: {
            participants: {
              has: userProfile.id,
            },
            organizationId: session.org,
          },
        }),
        prisma.message.count({
          where: {
            senderId: userProfile.id,
          },
        }),
        prisma.message.count({
          where: {
            conversation: {
              participants: {
                has: userProfile.id,
              },
            },
            senderId: {
              not: userProfile.id,
            },
            readBy: {
              not: {
                has: userProfile.id,
              },
            },
          },
        }),
      ]);

    return {
      success: true,
      stats: {
        totalConversations,
        totalMessages,
        unreadMessages,
      },
    };
  } catch (error) {
    console.error("Error fetching messaging stats:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch messaging statistics",
    };
  }
}

// Search conversations
export async function searchConversations(
  searchTerm: string,
  page = 1,
  limit = 20
) {
  try {
    const session = await getSession();

    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    // Search conversations by participant names or recent message content
    const conversations = await prisma.conversation.findMany({
      where: {
        participants: {
          has: userProfile.id,
        },
        organizationId: session.org,
        OR: [
          {
            messages: {
              some: {
                content: {
                  contains: searchTerm,
                  mode: "insensitive",
                },
              },
            },
          },
        ],
      },
      include: {
        messages: {
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
          include: {
            sender: {
              select: {
                firstName: true,
                lastName: true,
                profilePicture: true,
                user: {
                  select: {
                    name: true,
                    image: true,
                  },
                },
              },
            },
          },
        },
      },
      skip: (page - 1) * limit,
      take: limit,
      orderBy: {
        lastMessageAt: "desc",
      },
    });

    // Get other participants' info for each conversation
    const conversationsWithParticipants = await Promise.all(
      conversations.map(async (conversation) => {
        const otherParticipantIds = conversation.participants.filter(
          (id) => id !== userProfile.id
        );

        const otherParticipants = await prisma.alumniProfile.findMany({
          where: {
            id: {
              in: otherParticipantIds,
            },
            OR: [
              {
                firstName: {
                  contains: searchTerm,
                },
              },
              {
                lastName: {
                  contains: searchTerm,
                },
              },
            ],
          },
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePicture: true,
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        });

        return {
          ...conversation,
          otherParticipants,
          lastMessage: conversation.messages[0] || null,
        };
      })
    );

    // Filter out conversations with no matching participants if searching by name
    const filteredConversations = conversationsWithParticipants.filter(
      (conv) => conv.otherParticipants.length > 0 || conv.messages.length > 0
    );

    return {
      success: true,
      conversations: filteredConversations,
      pagination: {
        page,
        limit,
        total: filteredConversations.length,
        totalPages: Math.ceil(filteredConversations.length / limit),
      },
    };
  } catch (error) {
    console.error("Error searching conversations:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to search conversations",
    };
  }
}

// Send typing indicator
export async function sendTypingIndicator(
  conversationId: string,
  isTyping: boolean
) {
  try {
    const session = await getSession();

    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    // Verify user is part of the conversation
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
    });

    if (!conversation || !conversation.participants.includes(userProfile.id)) {
      return {
        success: false,
        error: "Conversation not found or access denied",
      };
    }

    // Send typing indicator to other participants
    const otherParticipants = conversation.participants.filter(
      (participantId) => participantId !== userProfile.id
    );

    for (const participantId of otherParticipants) {
      try {
        await pusherServer.trigger(
          `private-user-${participantId}`,
          "typing-indicator",
          {
            conversationId,
            userId: userProfile.id,
            user: {
              id: userProfile.id,
              firstName: userProfile.firstName,
              lastName: userProfile.lastName,
              profilePicture: userProfile.profilePicture,
            },
            isTyping,
            timestamp: new Date(),
          }
        );
      } catch (pusherError) {
        console.error("Error sending typing indicator:", pusherError);
        // Don't fail the operation if Pusher fails
      }
    }

    return { success: true };
  } catch (error) {
    console.error("Error sending typing indicator:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to send typing indicator",
    };
  }
}

// Send user presence update
export async function updateUserPresence(
  status: "online" | "away" | "offline"
) {
  try {
    const session = await getSession();

    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    // Get all conversations where user is a participant
    const conversations = await prisma.conversation.findMany({
      where: {
        participants: {
          has: userProfile.id,
        },
        organizationId: session.org,
      },
    });

    // Send presence update to all participants in all conversations
    const notifiedUsers = new Set<string>();

    for (const conversation of conversations) {
      const otherParticipants = conversation.participants.filter(
        (participantId) => participantId !== userProfile.id
      );

      for (const participantId of otherParticipants) {
        // Avoid sending duplicate notifications to the same user
        if (!notifiedUsers.has(participantId)) {
          try {
            await pusherServer.trigger(
              `private-user-${participantId}`,
              "user-presence",
              {
                userId: userProfile.id,
                user: {
                  id: userProfile.id,
                  firstName: userProfile.firstName,
                  lastName: userProfile.lastName,
                  profilePicture: userProfile.profilePicture,
                },
                status,
                timestamp: new Date(),
              }
            );
            notifiedUsers.add(participantId);
          } catch (pusherError) {
            console.error("Error sending presence update:", pusherError);
            // Don't fail the operation if Pusher fails
          }
        }
      }
    }

    return { success: true, notifiedUsers: notifiedUsers.size };
  } catch (error) {
    console.error("Error updating user presence:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update user presence",
    };
  }
}
