"use server";

import { getSession } from "@/actions/account/user";
import { prisma } from "@/prisma/prisma";

type ConnectionStatus = "PENDING" | "ACCEPTED" | "DECLINED";

// Send connection request
export async function sendConnectionRequest(recipientId: string) {
  try {
    const session = await getSession();

    // Check if recipient exists
    const recipient = await prisma.alumniProfile.findUnique({
      where: { userId: recipientId },
    });

    if (!recipient) {
      return { success: false, error: "Recipient not found" };
    }

    // Check if sender has a profile
    const sender = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!sender) {
      return {
        success: false,
        error: "You must create a profile before connecting with others",
      };
    }

    // Check if connection already exists
    const existingConnection = await prisma.connection.findFirst({
      where: {
        OR: [
          {
            senderId: sender.id,
            recipientId: recipient.id,
          },
          {
            senderId: recipient.id,
            recipientId: sender.id,
          },
        ],
      },
    });

    if (existingConnection) {
      if (existingConnection.status === "PENDING") {
        return { success: false, error: "Connection request already sent" };
      } else if (existingConnection.status === "ACCEPTED") {
        return { success: false, error: "Already connected" };
      } else if (existingConnection.status === "DECLINED") {
        // If previously declined, allow to send again by updating the existing record
        const updatedConnection = await prisma.connection.update({
          where: { id: existingConnection.id },
          data: {
            status: "PENDING",
            updatedAt: new Date(),
          },
        });

        return { success: true, connection: updatedConnection };
      }
    }

    // Create new connection request
    const newConnection = await prisma.connection.create({
      data: {
        senderId: sender.id,
        recipientId: recipient.id,
        status: "PENDING",
        organizationId: session.org,
      },
    });

    return { success: true, connection: newConnection };
  } catch (error) {
    console.error("Error sending connection request:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to send connection request",
    };
  }
}

// Accept or decline connection request
export async function respondToConnectionRequest(
  connectionId: string,
  accept: boolean
) {
  try {
    const session = await getSession();

    // Find the connection request
    const connection = await prisma.connection.findUnique({
      where: { id: connectionId },
      include: {
        recipient: true,
      },
    });

    if (!connection) {
      return { success: false, error: "Connection request not found" };
    }

    // Verify the current user is the recipient
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile || connection.recipientId !== userProfile.id) {
      return {
        success: false,
        error: "Unauthorized to respond to this connection request",
      };
    }

    // Update connection status
    const updatedConnection = await prisma.connection.update({
      where: { id: connectionId },
      data: {
        status: accept ? "ACCEPTED" : "DECLINED",
        updatedAt: new Date(),
      },
    });

    return { success: true, connection: updatedConnection };
  } catch (error) {
    console.error("Error responding to connection request:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to respond to connection request",
    };
  }
}

// Get all connections for current user
export async function getMyConnections(
  status?: ConnectionStatus,
  page = 1,
  limit = 20
) {
  try {
    const session = await getSession();

    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    // Build where clause
    const where: any = {
      OR: [{ senderId: userProfile.id }, { recipientId: userProfile.id }],
      organizationId: session.org,
    };

    // Filter by status if provided
    if (status) {
      where.status = status;
    }

    // Get connections with pagination
    const [connections, total] = await Promise.all([
      prisma.connection.findMany({
        where,
        include: {
          sender: {
            include: {
              user: {
                select: {
                  name: true,
                  image: true,
                },
              },
            },
          },
          recipient: {
            include: {
              user: {
                select: {
                  name: true,
                  image: true,
                },
              },
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          updatedAt: "desc",
        },
      }),
      prisma.connection.count({ where }),
    ]);

    // Transform connections to show the other person's info
    const transformedConnections = connections.map((connection) => {
      const isUserSender = connection.senderId === userProfile.id;
      const otherPerson = isUserSender
        ? connection.recipient
        : connection.sender;

      return {
        id: connection.id,
        status: connection.status,
        createdAt: connection.createdAt,
        updatedAt: connection.updatedAt,
        isUserSender,
        otherPerson: {
          id: otherPerson.id,
          firstName: otherPerson.firstName,
          lastName: otherPerson.lastName,
          profilePicture: otherPerson.profilePicture,
          currentPosition: otherPerson.currentPosition,
          currentCompany: otherPerson.currentCompany,
          user: otherPerson.user,
        },
      };
    });

    return {
      success: true,
      connections: transformedConnections,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Error fetching connections:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch connections",
    };
  }
}

// Remove connection
export async function removeConnection(connectionId: string) {
  try {
    const session = await getSession();

    // Find the connection
    const connection = await prisma.connection.findUnique({
      where: { id: connectionId },
    });

    if (!connection) {
      return { success: false, error: "Connection not found" };
    }

    // Verify the current user is part of the connection
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    if (
      connection.senderId !== userProfile.id &&
      connection.recipientId !== userProfile.id
    ) {
      return {
        success: false,
        error: "Unauthorized to remove this connection",
      };
    }

    // Delete the connection
    await prisma.connection.delete({
      where: { id: connectionId },
    });

    return { success: true };
  } catch (error) {
    console.error("Error removing connection:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to remove connection",
    };
  }
}

// Get connection suggestions
export async function getConnectionSuggestions(limit = 10) {
  try {
    const session = await getSession();

    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
      include: {
        sentConnections: true,
        receivedConnections: true,
      },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    // Get IDs of profiles the user is already connected with
    const existingConnectionIds = [
      ...userProfile.sentConnections.map((c) => c.recipientId),
      ...userProfile.receivedConnections.map((c) => c.senderId),
    ];

    // Add user's own ID to exclude
    existingConnectionIds.push(userProfile.id);

    // Find suggestions based on similar program, graduation year, or skills
    const suggestions = await prisma.alumniProfile.findMany({
      where: {
        organizationId: session.org,
        id: {
          notIn: existingConnectionIds,
        },
        profileVisibility: {
          in: ["PUBLIC", "ALUMNI_ONLY"],
        },
        OR: [
          // Same program
          { programType: userProfile.programType },
          // Similar graduation year (±2 years)
          {
            graduationYear: {
              gte: userProfile.graduationYear - 2,
              lte: userProfile.graduationYear + 2,
            },
          },
          // Matching skills (user's wanted skills match others' offered skills)
          {
            skillsOffered: {
              hasSome: userProfile.skillsWanted || [],
            },
          },
          // Matching skills (user's offered skills match others' wanted skills)
          {
            skillsWanted: {
              hasSome: userProfile.skillsOffered || [],
            },
          },
          // Mentorship matching
          {
            offeringMentorship: userProfile.seekingMentorship,
          },
          {
            seekingMentorship: userProfile.offeringMentorship,
          },
        ],
      },
      include: {
        user: {
          select: {
            name: true,
            image: true,
          },
        },
      },
      take: limit,
    });

    return { success: true, suggestions };
  } catch (error) {
    console.error("Error fetching connection suggestions:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch connection suggestions",
    };
  }
}

// Get connection statistics
export async function getConnectionStats() {
  try {
    const session = await getSession();

    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });

    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }

    // Get connection counts
    const [pendingRequests, acceptedConnections, totalAlumni] =
      await Promise.all([
        prisma.connection.count({
          where: {
            recipientId: userProfile.id,
            status: "PENDING",
          },
        }),
        prisma.connection.count({
          where: {
            OR: [{ senderId: userProfile.id }, { recipientId: userProfile.id }],
            status: "ACCEPTED",
          },
        }),
        prisma.alumniProfile.count({
          where: {
            organizationId: session.org,
            profileVisibility: {
              in: ["PUBLIC", "ALUMNI_ONLY"],
            },
          },
        }),
      ]);

    return {
      success: true,
      stats: {
        pendingRequests,
        acceptedConnections,
        totalAlumni,
        connectionRate:
          totalAlumni > 0 ? (acceptedConnections / totalAlumni) * 100 : 0,
      },
    };
  } catch (error) {
    console.error("Error fetching connection stats:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch connection statistics",
    };
  }
}
