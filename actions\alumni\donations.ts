"use server";

import { getSession } from "@/actions/account/user";
import { prisma } from "@/prisma/prisma";

// Donation type
type DonationType = 'ONE_TIME' | 'RECURRING';

// Donation status
type DonationStatus = 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'REFUNDED';

// Recurring donation frequency
type RecurringFrequency = 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY';

// Campaign status
type CampaignStatus = 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED';

interface CreateDonationData {
  amount: number;
  donationType: DonationType;
  campaignId?: string;
  message?: string;
  isAnonymous?: boolean;
  recurringFrequency?: RecurringFrequency;
  recurringEndDate?: Date;
  paymentMethodId?: string;
}

interface CreateCampaignData {
  title: string;
  description: string;
  goalAmount: number;
  startDate: Date;
  endDate: Date;
  category: string;
  images?: string[];
  isPublic: boolean;
}

interface UpdateCampaignData extends Partial<CreateCampaignData> {
  id: string;
  status?: CampaignStatus;
}

// Process a donation
export async function processDonation(data: CreateDonationData) {
  try {
    const session = await getSession();
    
    // Get donor's profile
    const donorProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!donorProfile) {
      return { success: false, error: "You must create a profile before making donations" };
    }
    
    // Validate amount
    if (data.amount <= 0) {
      return { success: false, error: "Donation amount must be greater than 0" };
    }
    
    if (data.amount > 100000) {
      return { success: false, error: "Donation amount cannot exceed $100,000" };
    }
    
    // Validate campaign if provided
    let campaign = null;
    if (data.campaignId) {
      campaign = await prisma.donationCampaign.findUnique({
        where: { id: data.campaignId },
      });
      
      if (!campaign) {
        return { success: false, error: "Campaign not found" };
      }
      
      if (campaign.status !== 'ACTIVE') {
        return { success: false, error: "Campaign is not active" };
      }
      
      if (new Date() > campaign.endDate) {
        return { success: false, error: "Campaign has ended" };
      }
    }
    
    // Validate recurring donation data
    if (data.donationType === 'RECURRING') {
      if (!data.recurringFrequency) {
        return { success: false, error: "Recurring frequency is required for recurring donations" };
      }
      
      if (data.recurringEndDate && data.recurringEndDate <= new Date()) {
        return { success: false, error: "Recurring end date must be in the future" };
      }
    }
    
    // Create the donation
    const donation = await prisma.donation.create({
      data: {
        amount: data.amount,
        donationType: data.donationType,
        status: 'PENDING', // Would be updated by payment processor
        message: data.message,
        isAnonymous: data.isAnonymous || false,
        recurringFrequency: data.recurringFrequency,
        recurringEndDate: data.recurringEndDate ? new Date(data.recurringEndDate) : null,
        paymentMethodId: data.paymentMethodId,
        donorId: donorProfile.id,
        campaignId: data.campaignId,
        organizationId: session.org,
      },
      include: {
        donor: {
          select: {
            firstName: true,
            lastName: true,
            profilePicture: true,
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
        campaign: {
          select: {
            title: true,
            description: true,
            goalAmount: true,
            currentAmount: true,
          },
        },
      },
    });
    
    // If this is for a campaign, update the campaign's current amount
    if (data.campaignId && data.amount > 0) {
      await prisma.donationCampaign.update({
        where: { id: data.campaignId },
        data: {
          currentAmount: {
            increment: data.amount,
          },
        },
      });
    }
    
    // TODO: Integrate with payment processor (Stripe, PayPal, etc.)
    // For now, we'll simulate successful payment
    await prisma.donation.update({
      where: { id: donation.id },
      data: {
        status: 'COMPLETED',
        processedAt: new Date(),
      },
    });
    
    return { success: true, donation };
  } catch (error) {
    console.error("Error processing donation:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to process donation" 
    };
  }
}

// Get donation history
export async function getDonationHistory(
  page = 1,
  limit = 20,
  filters: {
    donationType?: DonationType;
    status?: DonationStatus;
    campaignId?: string;
    startDate?: Date;
    endDate?: Date;
  } = {}
) {
  try {
    const session = await getSession();
    
    // Get donor's profile
    const donorProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!donorProfile) {
      return { success: false, error: "Profile not found" };
    }
    
    // Build where clause
    const whereClause: any = {
      donorId: donorProfile.id,
      organizationId: session.org,
    };
    
    if (filters.donationType) {
      whereClause.donationType = filters.donationType;
    }
    
    if (filters.status) {
      whereClause.status = filters.status;
    }
    
    if (filters.campaignId) {
      whereClause.campaignId = filters.campaignId;
    }
    
    if (filters.startDate || filters.endDate) {
      whereClause.createdAt = {};
      if (filters.startDate) {
        whereClause.createdAt.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        whereClause.createdAt.lte = new Date(filters.endDate);
      }
    }
    
    // Get donations with pagination
    const [donations, total] = await Promise.all([
      prisma.donation.findMany({
        where: whereClause,
        include: {
          campaign: {
            select: {
              title: true,
              description: true,
              goalAmount: true,
              currentAmount: true,
              category: true,
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      prisma.donation.count({
        where: whereClause,
      }),
    ]);
    
    return {
      success: true,
      donations,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Error fetching donation history:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to fetch donation history" 
    };
  }
}

// Get recurring donations
export async function getRecurringDonations(page = 1, limit = 20) {
  try {
    const session = await getSession();
    
    // Get donor's profile
    const donorProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!donorProfile) {
      return { success: false, error: "Profile not found" };
    }
    
    // Get active recurring donations
    const [recurringDonations, total] = await Promise.all([
      prisma.donation.findMany({
        where: {
          donorId: donorProfile.id,
          donationType: 'RECURRING',
          status: 'COMPLETED',
          OR: [
            { recurringEndDate: null },
            { recurringEndDate: { gt: new Date() } },
          ],
        },
        include: {
          campaign: {
            select: {
              title: true,
              description: true,
              goalAmount: true,
              currentAmount: true,
              category: true,
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      prisma.donation.count({
        where: {
          donorId: donorProfile.id,
          donationType: 'RECURRING',
          status: 'COMPLETED',
          OR: [
            { recurringEndDate: null },
            { recurringEndDate: { gt: new Date() } },
          ],
        },
      }),
    ]);
    
    return {
      success: true,
      recurringDonations,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Error fetching recurring donations:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to fetch recurring donations" 
    };
  }
}

// Cancel recurring donation
export async function cancelRecurringDonation(donationId: string) {
  try {
    const session = await getSession();
    
    // Get donor's profile
    const donorProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!donorProfile) {
      return { success: false, error: "Profile not found" };
    }
    
    // Find the donation
    const donation = await prisma.donation.findUnique({
      where: { id: donationId },
    });
    
    if (!donation) {
      return { success: false, error: "Donation not found" };
    }
    
    // Check if user owns this donation
    if (donation.donorId !== donorProfile.id) {
      return { success: false, error: "You can only cancel your own donations" };
    }
    
    // Check if it's a recurring donation
    if (donation.donationType !== 'RECURRING') {
      return { success: false, error: "This is not a recurring donation" };
    }
    
    // Check if it's already cancelled
    if (donation.status === 'CANCELLED') {
      return { success: false, error: "This donation is already cancelled" };
    }
    
    // Cancel the recurring donation
    const updatedDonation = await prisma.donation.update({
      where: { id: donationId },
      data: {
        status: 'CANCELLED',
        recurringEndDate: new Date(),
        updatedAt: new Date(),
      },
    });
    
    return { success: true, donation: updatedDonation };
  } catch (error) {
    console.error("Error cancelling recurring donation:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to cancel recurring donation" 
    };
  }
}

// Create donation campaign
export async function createDonationCampaign(data: CreateCampaignData) {
  try {
    const session = await getSession();
    
    // Get organizer's profile
    const organizerProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!organizerProfile) {
      return { success: false, error: "You must create a profile before creating campaigns" };
    }
    
    // Validate dates
    if (new Date(data.startDate) >= new Date(data.endDate)) {
      return { success: false, error: "End date must be after start date" };
    }
    
    if (new Date(data.startDate) < new Date()) {
      return { success: false, error: "Start date cannot be in the past" };
    }
    
    // Validate goal amount
    if (data.goalAmount <= 0) {
      return { success: false, error: "Goal amount must be greater than 0" };
    }
    
    // Create the campaign
    const campaign = await prisma.donationCampaign.create({
      data: {
        title: data.title,
        description: data.description,
        goalAmount: data.goalAmount,
        currentAmount: 0,
        startDate: new Date(data.startDate),
        endDate: new Date(data.endDate),
        category: data.category,
        images: data.images || [],
        isPublic: data.isPublic,
        status: 'DRAFT',
        organizerId: organizerProfile.id,
        organizationId: session.org,
      },
      include: {
        organizer: {
          select: {
            firstName: true,
            lastName: true,
            profilePicture: true,
            currentPosition: true,
            currentCompany: true,
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
        _count: {
          select: {
            donations: true,
          },
        },
      },
    });
    
    return { success: true, campaign };
  } catch (error) {
    console.error("Error creating donation campaign:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to create donation campaign" 
    };
  }
}

// Update donation campaign
export async function updateDonationCampaign(data: UpdateCampaignData) {
  try {
    const session = await getSession();
    
    // Get organizer's profile
    const organizerProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!organizerProfile) {
      return { success: false, error: "Profile not found" };
    }
    
    // Find the campaign
    const existingCampaign = await prisma.donationCampaign.findUnique({
      where: { id: data.id },
    });
    
    if (!existingCampaign) {
      return { success: false, error: "Campaign not found" };
    }
    
    // Check if user is the organizer
    if (existingCampaign.organizerId !== organizerProfile.id) {
      return { success: false, error: "You can only update your own campaigns" };
    }
    
    // Validate dates if provided
    if (data.startDate && data.endDate) {
      if (new Date(data.startDate) >= new Date(data.endDate)) {
        return { success: false, error: "End date must be after start date" };
      }
    }
    
    // Update the campaign
    const updatedCampaign = await prisma.donationCampaign.update({
      where: { id: data.id },
      data: {
        ...(data.title && { title: data.title }),
        ...(data.description && { description: data.description }),
        ...(data.goalAmount && { goalAmount: data.goalAmount }),
        ...(data.startDate && { startDate: new Date(data.startDate) }),
        ...(data.endDate && { endDate: new Date(data.endDate) }),
        ...(data.category && { category: data.category }),
        ...(data.images && { images: data.images }),
        ...(data.isPublic !== undefined && { isPublic: data.isPublic }),
        ...(data.status && { status: data.status }),
        updatedAt: new Date(),
      },
      include: {
        organizer: {
          select: {
            firstName: true,
            lastName: true,
            profilePicture: true,
            currentPosition: true,
            currentCompany: true,
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
        _count: {
          select: {
            donations: true,
          },
        },
      },
    });
    
    return { success: true, campaign: updatedCampaign };
  } catch (error) {
    console.error("Error updating donation campaign:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to update donation campaign" 
    };
  }
}

// Get donation campaigns
export async function getDonationCampaigns(
  page = 1,
  limit = 20,
  filters: {
    status?: CampaignStatus;
    category?: string;
    organizerId?: string;
    isPublic?: boolean;
    search?: string;
  } = {}
) {
  try {
    const session = await getSession();
    
    // Build where clause
    const whereClause: any = {
      organizationId: session.org,
    };
    
    if (filters.status) {
      whereClause.status = filters.status;
    }
    
    if (filters.category) {
      whereClause.category = filters.category;
    }
    
    if (filters.organizerId) {
      whereClause.organizerId = filters.organizerId;
    }
    
    if (filters.isPublic !== undefined) {
      whereClause.isPublic = filters.isPublic;
    }
    
    if (filters.search) {
      whereClause.OR = [
        {
          title: {
            contains: filters.search,
            mode: 'insensitive',
          },
        },
        {
          description: {
            contains: filters.search,
            mode: 'insensitive',
          },
        },
        {
          category: {
            contains: filters.search,
            mode: 'insensitive',
          },
        },
      ];
    }
    
    // Get campaigns with pagination
    const [campaigns, total] = await Promise.all([
      prisma.donationCampaign.findMany({
        where: whereClause,
        include: {
          organizer: {
            select: {
              firstName: true,
              lastName: true,
              profilePicture: true,
              currentPosition: true,
              currentCompany: true,
              user: {
                select: {
                  name: true,
                  image: true,
                },
              },
            },
          },
          donations: {
            where: {
              status: 'COMPLETED',
            },
            select: {
              amount: true,
              donor: {
                select: {
                  firstName: true,
                  lastName: true,
                  profilePicture: true,
                },
              },
              isAnonymous: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 5, // Show recent donations
          },
          _count: {
            select: {
              donations: true,
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      prisma.donationCampaign.count({
        where: whereClause,
      }),
    ]);
    
    return {
      success: true,
      campaigns,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  } catch (error) {
    console.error("Error fetching donation campaigns:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to fetch donation campaigns" 
    };
  }
}

// Get donation statistics
export async function getDonationStats() {
  try {
    const session = await getSession();
    
    // Get user's profile
    const userProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!userProfile) {
      return { success: false, error: "Profile not found" };
    }
    
    // Get donation statistics
    const [totalDonated, totalDonations, recurringDonations, campaignsCreated] = await Promise.all([
      prisma.donation.aggregate({
        where: {
          donorId: userProfile.id,
          status: 'COMPLETED',
        },
        _sum: {
          amount: true,
        },
      }),
      prisma.donation.count({
        where: {
          donorId: userProfile.id,
          status: 'COMPLETED',
        },
      }),
      prisma.donation.count({
        where: {
          donorId: userProfile.id,
          donationType: 'RECURRING',
          status: 'COMPLETED',
          OR: [
            { recurringEndDate: null },
            { recurringEndDate: { gt: new Date() } },
          ],
        },
      }),
      prisma.donationCampaign.count({
        where: {
          organizerId: userProfile.id,
        },
      }),
    ]);
    
    return {
      success: true,
      stats: {
        totalDonated: totalDonated._sum.amount || 0,
        totalDonations,
        recurringDonations,
        campaignsCreated,
      },
    };
  } catch (error) {
    console.error("Error fetching donation stats:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to fetch donation statistics" 
    };
  }
}

// Delete donation campaign
export async function deleteDonationCampaign(campaignId: string) {
  try {
    const session = await getSession();
    
    // Get organizer's profile
    const organizerProfile = await prisma.alumniProfile.findUnique({
      where: { userId: session.userId },
    });
    
    if (!organizerProfile) {
      return { success: false, error: "Profile not found" };
    }
    
    // Find the campaign
    const campaign = await prisma.donationCampaign.findUnique({
      where: { id: campaignId },
      include: {
        _count: {
          select: {
            donations: true,
          },
        },
      },
    });
    
    if (!campaign) {
      return { success: false, error: "Campaign not found" };
    }
    
    // Check if user is the organizer
    if (campaign.organizerId !== organizerProfile.id) {
      return { success: false, error: "You can only delete your own campaigns" };
    }
    
    // Check if campaign has donations
    if (campaign._count.donations > 0) {
      return { success: false, error: "Cannot delete campaign with existing donations" };
    }
    
    // Delete the campaign
    await prisma.donationCampaign.delete({
      where: { id: campaignId },
    });
    
    return { success: true };
  } catch (error) {
    console.error("Error deleting donation campaign:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to delete donation campaign" 
    };
  }
}