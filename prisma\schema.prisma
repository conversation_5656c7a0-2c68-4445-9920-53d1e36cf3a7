generator client {
  provider = "prisma-client-js"
  output   = "./generated/client"
}

generator zod {
  provider                         = "zod-prisma-types"
  createOptionalDefaultValuesTypes = "true"
  writeNullishInModelTypes         = "true"
  createInputTypes                 = "false"
  output                           = "./generated/zod"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                   String                @id
  name                 String
  email                String
  emailVerified        Boolean
  image                String?
  role                 String?
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt
  sessions             Session[]
  accounts             Account[]
  notificationSettings NotificationSettings?
  notification         Notification[]
  Member               Member[]
  Invitation           Invitation[]
  Passkey              Passkey[]
  TwoFactor            TwoFactor[]
  alumniProfile        AlumniProfile?

  @@unique([email])
  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  userId    String?
  user      User?    @relation(fields: [userId], references: [id], onDelete: SetNull)

  activeOrganizationId String?

  token     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([token])
  @@index([userId])
  @@map("session")
}

model Account {
  id           String    @id
  accountId    String
  providerId   String
  userId       String
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken  String?
  refreshToken String?
  idToken      String?
  expiresAt    DateTime?
  password     String?

  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  @@index([userId])
  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model Organization {
  id          String       @id @default(uuid())
  name        String
  slug        String?
  logo        String?
  createdAt   DateTime     @default(now())
  metadata    String?
  members     Member[]
  invitations Invitation[]

  @@unique([slug])
  @@map("organization")
}

model Member {
  id             String       @id @default(uuid())
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String?
  user           User?        @relation(fields: [userId], references: [id], onDelete: SetNull)
  role           String
  createdAt      DateTime

  @@index([organizationId])
  @@index([userId])
  @@map("member")
}

model Invitation {
  id             String       @id
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String
  role           String?
  status         String
  expiresAt      DateTime
  inviterId      String?
  user           User?        @relation(fields: [inviterId], references: [id], onDelete: SetNull)

  @@index([organizationId])
  @@index([inviterId])
  @@map("invitation")
}

model Passkey {
  id             String    @id
  name           String?
  publicKey      String
  userId         String?
  user           User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  webauthnUserID String
  counter        Int
  deviceType     String
  backedUp       Boolean
  transports     String?
  createdAt      DateTime?
  credentialID   String

  @@index([userId])
  @@map("passkey")
}

model TwoFactor {
  id          String  @id
  secret      String
  backupCodes String
  userId      String?
  user        User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@map("twoFactor")
}

model Notification {
  id         String                @id @default(uuid())
  title      String
  message    String
  type       notification_type
  priority   notification_priority @default(low)
  created_at DateTime              @default(now())
  userId     String
  user       User                  @relation(fields: [userId], references: [id], onDelete: Cascade)

  read      Boolean   @default(false)
  readAt    DateTime?
  actionUrl String?

  @@index([userId, created_at])
  @@map("notification")
}

model NotificationSettings {
  id String @id @default(cuid())

  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  inApp Boolean @default(true)
  email Boolean @default(true)

  systemUpdates Boolean @default(true)
  newFeature    Boolean @default(true)
  securityAlert Boolean @default(true)
  message       Boolean @default(true)
  promotion     Boolean @default(true)
  reminder      Boolean @default(true)

  @@index([userId])
  @@map("notifications_settings")
}

enum notification_type {
  account
  system_update
  new_feature
  security_alert
  message
  promotion
  reminder
}

enum notification_priority {
  low
  medium
  high
  urgent
}

// Alumni Profile System
model AlumniProfile {
  id     String @id @default(uuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Personal Information
  firstName      String
  lastName       String
  bio            String?
  profilePicture String?

  // PROTEC History
  graduationYear Int
  programType    ProgramType
  centerLocation String

  // Career Information
  currentPosition String?
  currentCompany  String?
  industry        String?
  workLocation    String?

  // Contact Details
  linkedinUrl String?
  phoneNumber String?

  // Privacy Controls
  profileVisibility ProfileVisibility @default(ALUMNI_ONLY)
  emailVisible      Boolean           @default(false)
  phoneVisible      Boolean           @default(false)
  locationVisible   Boolean           @default(true)

  // Mentorship Features
  offeringMentorship Boolean @default(false)
  seekingMentorship  Boolean @default(false)
  skillsOffered      String? // JSON array of skills
  skillsWanted       String? // JSON array of skills

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  sentConnections     Connection[]        @relation("SentConnections")
  receivedConnections Connection[]        @relation("ReceivedConnections")
  sentMessages        Message[]           @relation("SentMessages")
  posts               Post[]
  comments            Comment[]
  postLikes           PostLike[]
  eventRegistrations  EventRegistration[]
  donations           Donation[]

  @@index([graduationYear])
  @@index([programType])
  @@index([industry])
  @@index([centerLocation])
  @@map("alumni_profile")
}

// Connection Management System
model Connection {
  id         String           @id @default(uuid())
  senderId   String
  sender     AlumniProfile    @relation("SentConnections", fields: [senderId], references: [id], onDelete: Cascade)
  receiverId String
  receiver   AlumniProfile    @relation("ReceivedConnections", fields: [receiverId], references: [id], onDelete: Cascade)
  status     ConnectionStatus @default(PENDING)
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt

  @@unique([senderId, receiverId])
  @@index([senderId])
  @@index([receiverId])
  @@index([status])
  @@map("connection")
}

// Conversation-based Messaging System
model Conversation {
  id             String   @id @default(uuid())
  participants   String[] // Array of AlumniProfile IDs
  organizationId String
  lastMessageAt  DateTime @default(now())
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relationships
  messages Message[]

  @@index([organizationId])
  @@index([lastMessageAt])
  @@map("conversation")
}

model Message {
  id             String        @id @default(uuid())
  content        String        @db.Text
  senderId       String
  sender         AlumniProfile @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  conversationId String
  conversation   Conversation  @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  organizationId String
  readBy         String[]      @default([]) // Array of AlumniProfile IDs who have read this message
  isDeleted      Boolean       @default(false)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  @@index([senderId])
  @@index([conversationId])
  @@index([createdAt])
  @@index([organizationId])
  @@map("message")
}

// Alumni Posts System
model Post {
  id        String        @id @default(uuid())
  authorId  String
  author    AlumniProfile @relation(fields: [authorId], references: [id], onDelete: Cascade)
  title     String?
  content   String        @db.Text
  type      PostType      @default(GENERAL)
  imageUrl  String?
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Relationships
  comments Comment[]
  likes    PostLike[]

  @@index([authorId])
  @@index([type])
  @@index([createdAt])
  @@map("post")
}

// Post Engagement - Comments
model Comment {
  id        String        @id @default(uuid())
  postId    String
  post      Post          @relation(fields: [postId], references: [id], onDelete: Cascade)
  authorId  String
  author    AlumniProfile @relation(fields: [authorId], references: [id], onDelete: Cascade)
  content   String        @db.Text
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  @@index([postId])
  @@index([authorId])
  @@index([createdAt])
  @@map("comment")
}

// Post Engagement - Likes
model PostLike {
  id        String        @id @default(uuid())
  postId    String
  post      Post          @relation(fields: [postId], references: [id], onDelete: Cascade)
  userId    String
  user      AlumniProfile @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt DateTime      @default(now())

  @@unique([postId, userId])
  @@index([postId])
  @@index([userId])
  @@map("post_like")
}

// Events System
model Event {
  id            String    @id @default(uuid())
  title         String
  description   String    @db.Text
  type          EventType
  startDateTime DateTime
  endDateTime   DateTime
  location      String?
  isVirtual     Boolean   @default(false)
  virtualUrl    String?
  maxAttendees  Int?
  imageUrl      String?
  organizerInfo String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relationships
  registrations EventRegistration[]

  @@index([type])
  @@index([startDateTime])
  @@index([isVirtual])
  @@map("event")
}

// Event Registration System
model EventRegistration {
  id        String             @id @default(uuid())
  eventId   String
  event     Event              @relation(fields: [eventId], references: [id], onDelete: Cascade)
  userId    String
  user      AlumniProfile      @relation(fields: [userId], references: [id], onDelete: Cascade)
  status    RegistrationStatus @default(REGISTERED)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt

  @@unique([eventId, userId])
  @@index([eventId])
  @@index([userId])
  @@index([status])
  @@map("event_registration")
}

// Official News System
model NewsArticle {
  id          String       @id @default(uuid())
  title       String
  content     String       @db.Text
  excerpt     String?
  category    NewsCategory @default(GENERAL)
  imageUrl    String?
  published   Boolean      @default(false)
  publishedAt DateTime?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  @@index([category])
  @@index([published])
  @@index([publishedAt])
  @@map("news_article")
}

// Donation System
model Donation {
  id            String        @id @default(uuid())
  donorId       String
  donor         AlumniProfile @relation(fields: [donorId], references: [id], onDelete: Cascade)
  amount        Decimal       @db.Decimal(10, 2)
  currency      String        @default("ZAR")
  type          DonationType  @default(ONE_TIME)
  purpose       String?
  isAnonymous   Boolean       @default(false)
  paymentMethod String?
  transactionId String?       @unique
  paymentStatus PaymentStatus @default(PENDING)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@index([donorId])
  @@index([type])
  @@index([paymentStatus])
  @@index([createdAt])
  @@map("donation")
}

// Enums for Alumni System
enum ProgramType {
  ENGINEERING
  BUSINESS
  SCIENCE
  TECHNOLOGY
  OTHER
}

enum ProfileVisibility {
  PUBLIC
  ALUMNI_ONLY
  PRIVATE
}

enum ConnectionStatus {
  PENDING
  ACCEPTED
  DECLINED
  BLOCKED
}

enum PostType {
  GENERAL
  SUCCESS_STORY
  JOB_OPPORTUNITY
  MENTORSHIP
  ANNOUNCEMENT
}

enum EventType {
  WORKSHOP
  NETWORKING
  CONFERENCE
  WEBINAR
  SOCIAL
  FUNDRAISING
  MENTORSHIP
}

enum RegistrationStatus {
  REGISTERED
  ATTENDED
  NO_SHOW
  CANCELLED
}

enum NewsCategory {
  GENERAL
  EVENTS
  SUCCESS_STORIES
  OPPORTUNITIES
  ANNOUNCEMENTS
}

enum DonationType {
  ONE_TIME
  MONTHLY
  QUARTERLY
  ANNUAL
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  CANCELLED
}
